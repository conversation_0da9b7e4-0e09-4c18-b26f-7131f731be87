import { Tooltip } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";
import { PresetsCommand } from "@cscs-agent/presets";

interface PreviewButtonProps {
  id: string;
  name: string;
  version: string;
}

const PreviewButton: React.FC<PreviewButtonProps> = (props) => {
  const { id, name, version } = props ?? {};
  const runner = useCommandRunner();

  const handlePreview = () => {
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@DynamicPage/SidePanelPreview",
      widgetProps: {
        id,
        name,
      },
    });

    runner(BuildInCommand.OpenSidePanel);

    runner(PresetsCommand.CloseSideBar);
  };

  return (
    <Tooltip title="点击预览">
      <div
        className="ats:flex ats:justify-between ats:items-center ats:bg-white ats:my-4 ats:px-4 ats:py-4 ats:border ats:border-gray-300 ats:rounded-md ats:w-[300px] ats:cursor-pointer"
        style={{
          boxShadow: "0px 3px 4px -3px rgba(0,0,0,0.08)",
        }}
        onClick={handlePreview}
      >
        <div className="ats:flex-1">
          <Icon
            icon="Web"
            style={{
              color: "#6C90F2",
            }}
          />
          <span className="ats:ml-2">{name}</span>
        </div>
        <div
          hidden={!version}
          className="ats:bg-[rgba(172,174,189,0.10)] ats:p-1 ats:rounded-sm ats:text-black-65 ats:text-xs"
        >
          {version}
        </div>
      </div>
    </Tooltip>
  );
};

export default PreviewButton;
