# Slate RangePicker Component

Slate编辑器的RangePicker组件，基于Ant Design的DatePicker.RangePicker组件构建，用于选择日期范围。

## 功能特性

- ✅ 支持日期范围选择
- ✅ 支持时间范围选择（可选）
- ✅ 支持自定义日期格式
- ✅ 支持自定义分隔符
- ✅ 支持清除功能
- ✅ 支持禁用状态
- ✅ 支持工具提示
- ✅ 与现有的Slate编辑器完全兼容
- ✅ 完整的TypeScript类型支持

## 使用方式

### 1. 使用HTML标签方式插入

```javascript
// 基本日期范围选择器
const rangePickerText = `<embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="," tooltips="选择日期范围" disabled="false"></embedded-rangepicker>`;
editorRef.current?.insertText(rangePickerText);

// 带时间的日期范围选择器
const dateTimeRangePickerText = `<embedded-rangepicker placeholder="开始时间,结束时间" defaultValue="," tooltips="选择日期时间范围" disabled="false" format="YYYY-MM-DD HH:mm:ss" showTime="true"></embedded-rangepicker>`;
editorRef.current?.insertText(dateTimeRangePickerText);

// 自定义分隔符的日期范围选择器
const customSeparatorText = `<embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="," tooltips="选择日期范围" disabled="false" separator=" 至 "></embedded-rangepicker>`;
editorRef.current?.insertText(customSeparatorText);
```

### 2. 使用Slate插件方式

```javascript
import { Transforms } from 'slate';

// 插入基本日期范围选择器
Transforms.insertNodes(editor, {
  type: 'embedded-rangepicker',
  placeholder: ['开始日期', '结束日期'],
  defaultValue: ['', ''],
  tooltips: '选择日期范围',
  disabled: false,
  format: 'YYYY-MM-DD',
  showTime: false,
  allowClear: true,
  separator: '~',
  children: [{ text: '' }]
});

// 插入日期时间范围选择器
Transforms.insertNodes(editor, {
  type: 'embedded-rangepicker',
  placeholder: ['开始时间', '结束时间'],
  defaultValue: ['2024-01-01 00:00:00', '2024-12-31 23:59:59'],
  tooltips: '选择日期时间范围',
  disabled: false,
  format: 'YYYY-MM-DD HH:mm:ss',
  showTime: true,
  allowClear: true,
  separator: ' ~ ',
  children: [{ text: '' }]
});
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `placeholder` | `[string, string]` | `["开始日期", "结束日期"]` | 输入框占位符，数组形式 |
| `defaultValue` | `[string, string]` | `["", ""]` | 默认选中的日期范围 |
| `tooltips` | `string` | - | 鼠标悬停时显示的提示信息 |
| `disabled` | `boolean` | `false` | 是否禁用组件 |
| `format` | `string` | `"YYYY-MM-DD"` | 日期格式 |
| `showTime` | `boolean` | `false` | 是否显示时间选择 |
| `allowClear` | `boolean` | `true` | 是否允许清除 |
| `separator` | `string` | `"~"` | 日期范围分隔符 |

## 使用示例

### 基本日期范围选择

```html
<embedded-rangepicker 
  placeholder="开始日期,结束日期" 
  defaultValue="," 
  tooltips="选择日期范围" 
  disabled="false">
</embedded-rangepicker>
```

### 带时间的日期范围选择

```html
<embedded-rangepicker 
  placeholder="开始时间,结束时间" 
  defaultValue="2024-01-01 00:00:00,2024-12-31 23:59:59" 
  tooltips="选择日期时间范围" 
  disabled="false" 
  format="YYYY-MM-DD HH:mm:ss" 
  showTime="true">
</embedded-rangepicker>
```

### 月份范围选择器

```html
<embedded-rangepicker 
  placeholder="开始月份,结束月份" 
  defaultValue="2024-01,2024-12" 
  tooltips="选择月份范围" 
  disabled="false" 
  format="YYYY-MM">
</embedded-rangepicker>
```

### 自定义分隔符

```html
<embedded-rangepicker 
  placeholder="开始日期,结束日期" 
  defaultValue="," 
  tooltips="选择日期范围" 
  disabled="false" 
  separator=" 至 ">
</embedded-rangepicker>
```

## 注意事项

1. `placeholder` 和 `defaultValue` 在HTML标签中使用逗号分隔的字符串形式
2. `defaultValue` 应该符合指定的 `format` 格式
3. 当 `showTime` 为 `true` 时，建议使用包含时间的格式如 `YYYY-MM-DD HH:mm:ss`
4. 组件会自动处理日期值的格式化和验证
5. 选中的日期范围会自动更新到 `defaultValue` 属性中，便于序列化和持久化
6. 在HTML标签中，空的日期值用空字符串表示，如 `defaultValue=","`

## 工具函数

组件提供了一些实用的工具函数：

```javascript
import { 
  getRangePickerValue, 
  setRangePickerValue, 
  clearRangePickerValue 
} from './RangePickerElement.example';

// 获取范围选择器的值
const range = getRangePickerValue(element);

// 设置范围选择器的值
setRangePickerValue(element, '2024-01-01', '2024-12-31');

// 清空范围选择器的值
clearRangePickerValue(element);
```
