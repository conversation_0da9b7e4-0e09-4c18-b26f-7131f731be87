#!/usr/bin/env node

/**
 * CSCS Agent CLI - Compiled entry point
 * This file imports and runs the compiled TypeScript CLI
 */

import { existsSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath, pathToFileURL } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Try to import the compiled CLI
const libPath = join(__dirname, "..", "lib", "cli.js");
const srcPath = join(__dirname, "..", "src", "cli.js");

let cliModule;

if (existsSync(libPath)) {
  // Use compiled version
  cliModule = await import(pathToFileURL(libPath).href);
} else if (existsSync(srcPath)) {
  // Fallback to source (for development)
  cliModule = await import(pathToFileURL(srcPath).href);
} else {
  console.error('CLI module not found. Please run "npm run build" first.');
  process.exit(1);
}
