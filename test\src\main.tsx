import "@@/@cscs-agent/core/dist/agent-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
import "@@/@cscs-agent/icons/dist/icons.css";

import "./styles.css";

import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router";

import { createDefaultRouter, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, Login, defaultAuthGuard } from "@cscs-agent/presets";

import Home from "./pages/home";

// 初始化应用
initApp({
  loginUrl: "/login", // 登录页面地址，默认为 /login，如果需要跳转到第三方登录页，可以设置为第三方登录页地址
}).then(() => {
  // 创建路由
  const router = createDefaultRouter({
    pages: {
      home: {
        Component: Home,
      },
      chat: {
        Component: Chat,
      },
      agentHome: {
        Component: AgentHome,
      },
      login: {
        enable: true,
        Component: Login,
      },
    },
    authGuard: defaultAuthGuard,
  });

  createRoot(document.getElementById("root")!).render(<RouterProvider router={router} />);
});
