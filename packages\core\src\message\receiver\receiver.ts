import { EventEmitter } from "eventemitter3";

import type { IMessagePackage, MessageChunk } from "@/types";
import { EventType, MessageErrorCode, MessagePackageStatus, MessagePackageType } from "@/types";

import { MessageError } from "./error";

/**
 * chunk 是构成消息包的基本单元，负责传输消息的具体内容。
 * 数据块可以是纯文本、结构化数据或其他类型的数据。chunk 的数量和顺序会影响消息的完整性和正确性，因此需要在解析时进行验证。
 * chunk 的数据结构为: 'data: { package_id, package_type, chunk_id, is_last, data }'，chunk_id 从0开始，is_last 为 true 表示最后一个 chunk。
 *
 * package 是消息的最小解析单元，包含一个或多个 chunk；客户端在接收 chunk 时先将 chunk 存储到 Buffer 中，直到接收到完整的 package 后才开始解析，解析时会验证 chunk 数量和顺序是否正确，确保消息的完整性和正确性。
 * package 的类型：text, structured，分别表示纯文本消息、结构化数据消息。
 * text 和 structured 类型的 package 包含一个消息体，text 和 structured 类型的 package 可以包含多个 chunk；
 */

/**
 * 消息接收器事件类型
 */
export enum MessageReceiverEvent {
  /** 接收到消息头 */
  HEADER_RECEIVED = "header_received",
  /** 接收到消息完成信号 */
  MESSAGE_FINISHED = "message_finished",
  /** 接收到完整的消息包 */
  PACKAGE_FINISHED_RECEIVED = "package_received",
  /** 正在接收消息包 */
  PACKAGE_RECEIVING = "package_receiving",
  /** 接收到新的消息块 */
  CHUNK_RECEIVED = "chunk_received",
  /** 接收完成 */
  DONE = "done",
  /** 取消 */
  CANCELLED = "cancelled",
  /** 接收出错 */
  ERROR = "error",
}

/**
 * 消息接收器
 * 负责从流中接收消息，解析消息块，并组装成完整的消息包
 */
export class MessageReceiver extends EventEmitter {
  /** 已处理的消息包 */
  packages: IMessagePackage[] = [];
  /** 当前处理的消息包ID */
  packageId: number = -1;
  /** 当前处理的消息包类型 */
  packageType: MessagePackageType | null = null;
  /** 消息块缓冲区 */
  packageBuffer: MessageChunk[] = [];
  /** 当前期望的消息块ID */
  chunkId: number = -1;
  /** 流读取器 */
  reader: ReadableStreamDefaultReader | null = null;
  /** 文本解码器 */
  decoder: TextDecoder | null = null;
  /** 是否正在接收 */
  private isReceiving: boolean = false;
  /** 是否已完成接收 */
  private isDone: boolean = false;

  /**
   * 构造函数
   * @param response Axios响应对象
   */
  constructor(private response: ReadableStream) {
    super();
    this.reader = this.response.getReader();
    this.decoder = new TextDecoder("utf-8");
  }

  /**
   * 开始接收消息
   * 持续从流中读取数据，直到流结束或出错
   */
  public receive(): void {
    if (this.isReceiving || this.isDone) {
      return;
    }

    this.isReceiving = true;
    this.readStream();
  }

  /**
   * 从流中读取数据
   * 递归调用，直到流结束或出错
   */
  private readStream(): void {
    if (!this.reader || this.isDone) {
      return;
    }

    this.reader
      .read()
      .then((result) => {
        if (result.done) {
          this.isDone = true;
          this.isReceiving = false;
          this.emit(MessageReceiverEvent.DONE);
          return;
        }

        const rawData = this.decoder?.decode(result.value);
        if (rawData) {
          this.processRawData(rawData);
        }

        // 继续读取
        this.readStream();
      })
      .catch((error) => {
        this.isReceiving = false;
        this.emit(MessageReceiverEvent.ERROR, new MessageError(error.message, MessageErrorCode.NetworkError, error));
      });
  }

  /**
   * 处理原始数据
   * 解析SSE格式的数据，提取消息块
   * @param rawData 原始数据
   */
  private processRawData(rawData: string): void {
    // 按行分割数据
    const lines = rawData.split("\n");

    for (const line of lines) {
      // 跳过空行
      if (!line.trim()) {
        continue;
      }

      // 解析SSE格式的数据行
      if (line.startsWith("data: ")) {
        try {
          const jsonStr = line.substring(6); // 去掉 'data: ' 前缀
          const chunk = JSON.parse(jsonStr) as MessageChunk;
          this.processChunk(chunk);
        } catch (error) {
          console.error("Failed to parse chunk data:", error);
          this.emit(
            MessageReceiverEvent.ERROR,
            new MessageError("Failed to parse chunk data", MessageErrorCode.ParsingError, error),
          );
        }
      }
    }
  }

  /**
   * 处理消息块
   * 验证消息块的有效性，并将其添加到缓冲区
   * @param chunk 消息块
   */
  private processChunk(chunk: MessageChunk): void {
    // 发出块接收事件
    this.emit(MessageReceiverEvent.CHUNK_RECEIVED, chunk);

    // 如果 event_type 为 Error，提前结束消息
    if (chunk.event_type === EventType.Error) {
      // 提前结束上一个包的接收
      this.processCompletePackage();
      let errorMessage = "";
      try {
        errorMessage = JSON.parse(chunk.data).error_message;
      } catch {
        errorMessage = "Unknown error";
      }
      this.processErrorPackage(chunk.data);
      this.emit(MessageReceiverEvent.ERROR, new MessageError(errorMessage, MessageErrorCode.ServerError, null));
      return;
    }

    // 如果是新的消息包，重置状态
    if (this.packageId !== chunk.package_id) {
      // 如果接收到新的包，但是没有收到上一个包的最后一个块，发出错误
      if (this.packageBuffer.length > 0) {
        const error = new Error(`Incomplete package: ${this.packageId}`);
        this.emit(MessageReceiverEvent.ERROR, error);
        // 提前结束上一个包的接收
        this.processCompletePackage();
      }
      this.resetPackageState(chunk);
    }

    // 更新当前块ID
    this.chunkId = chunk.chunk_id;

    // 将块添加到缓冲区
    this.packageBuffer.push(chunk);

    // 如果 event_type 为 End，表示消息结束
    if (chunk.event_type === EventType.End) {
      this.emit(MessageReceiverEvent.MESSAGE_FINISHED, chunk);
      return;
    }

    // 如果不是最后一个块，发出正在接收消息包事件
    // 只在接收到新的块时发出事件，避免过于频繁
    // if (!chunk.is_last) {
    //   this.emitPackageReceivingEvent();
    // }
    const status = chunk.is_last ? MessagePackageStatus.Finished : MessagePackageStatus.Loading;
    this.emitPackageReceivingEvent(status);
    // 如果是最后一个块，处理完整的消息包
    if (chunk.is_last) {
      this.processCompletePackage();
    }
  }

  /**
   * 重置消息包状态
   * 当接收到新的消息包时调用
   * @param chunk 消息块
   */
  private resetPackageState(chunk: MessageChunk): void {
    // 如果缓冲区不为空，说明上一个消息包未完成，发出错误
    if (this.packageBuffer.length > 0) {
      this.emit(
        MessageReceiverEvent.ERROR,
        new MessageError(`Incomplete package: ${this.packageId}`, MessageErrorCode.IncompletePackage, null),
      );
    }

    // 重置状态
    this.packageId = chunk.package_id;
    this.packageType = chunk.package_type;
    this.packageBuffer = [];
    this.chunkId = -1; // 重置为-1，因为我们期望第一个块的ID是0
  }

  /**
   * 处理完整的消息包
   * 当接收到消息包的最后一个块时调用
   */
  private processCompletePackage(): void {
    if (this.packageBuffer.length === 0) {
      return;
    }
    if (this.packageType === null) {
      this.emit(
        MessageReceiverEvent.ERROR,
        new MessageError("Package type is not set", MessageErrorCode.InvalidChunk, null),
      );
      return;
    }

    // 验证块的顺序和完整性
    const sortedChunks = [...this.packageBuffer].sort((a, b) => a.chunk_id - b.chunk_id);

    // 检查块是否连续
    for (let i = 0; i < sortedChunks.length; i++) {
      if (sortedChunks[i].chunk_id !== i) {
        this.emit(
          MessageReceiverEvent.ERROR,
          new MessageError(`Incomplete package: ${this.packageId}`, MessageErrorCode.InvalidChunk, null),
        );
        return;
      }
    }

    // 检查最后一个块是否为 is_last
    if (sortedChunks[sortedChunks.length - 1].is_last !== true) {
      this.emit(
        MessageReceiverEvent.ERROR,
        new MessageError(`Incomplete package: ${this.packageId}`, MessageErrorCode.InvalidChunk, null),
      );
      return;
    }

    // 合并所有块的数据
    const packageData = sortedChunks.map((chunk) => chunk.data).join("");

    // 添加到已处理的消息包列表
    const packageObj: IMessagePackage = {
      package_id: this.packageId,
      package_type: this.packageType,
      status: MessagePackageStatus.Finished,
      data: packageData,
    };
    this.packages.push(packageObj);

    // 发出包接收完成事件
    this.emit(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, packageObj);

    if (packageObj.package_id === 0) {
      // 发出消息头接收事件
      this.emit(MessageReceiverEvent.HEADER_RECEIVED, packageObj);
    }

    // 重置缓冲区，准备接收下一个消息包
    this.packageBuffer = [];
  }

  /** 处理错误信息包 */
  private processErrorPackage(data: string): void {
    const packageObj: IMessagePackage = {
      package_id: this.packageId,
      package_type: MessagePackageType.Error,
      status: MessagePackageStatus.Finished,
      data: data,
    };
    this.packages.push(packageObj);
    this.emit(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, packageObj);
    this.emit(MessageReceiverEvent.PACKAGE_RECEIVING, packageObj);
  }

  /**
   * 发出正在接收消息包事件
   * 创建一个临时的消息包对象，包含当前已接收的数据
   *
   * 此事件用于实现流式显示效果，允许UI在接收完整消息包之前就开始显示部分内容
   * 事件会传递一个状态为Loading的消息包对象，包含当前已接收的所有数据
   */
  private emitPackageReceivingEvent(status: MessagePackageStatus): void {
    if (this.packageType === null) {
      return;
    }

    // 验证块的顺序和完整性
    const sortedChunks = [...this.packageBuffer].sort((a, b) => a.chunk_id - b.chunk_id);

    // 合并当前已接收的所有块的数据
    const currentData = sortedChunks.map((chunk) => chunk.data).join("");

    // 创建临时消息包对象
    const packageObj: IMessagePackage = {
      package_id: this.packageId,
      package_type: this.packageType,
      status: status,
      data: currentData,
    };

    // 发出正在接收消息包事件
    this.emit(MessageReceiverEvent.PACKAGE_RECEIVING, packageObj);
  }

  /**
   * 停止接收
   * 释放资源
   */
  public stop(): void {
    if (this.reader && !this.isDone) {
      this.reader.cancel().catch(console.error);
    }
    this.isReceiving = false;
    this.isDone = true;
  }
}
