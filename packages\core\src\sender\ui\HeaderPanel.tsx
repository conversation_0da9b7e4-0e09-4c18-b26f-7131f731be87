import { Button } from "antd";
import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";
import { CloseOutlined } from "@ant-design/icons";

interface HeaderPanelProps {
  open: boolean;
  onClose: () => void;
}

const HeaderPanel: React.FC<HeaderPanelProps> = (props) => {
  const { open, onClose } = props;
  const agentConfig = useActiveAgentConfig();

  const Components = useMemo(() => {
    const widgets = agentConfig?.sender?.slots?.headerPanel?.widgets ?? [];
    return widgets.map((i) => i.component);
  }, [agentConfig]);

  const title = agentConfig?.sender?.slots?.headerPanel?.title ?? "工具面板";

  return (
    <div
      className={`ag:relative ${open ? "ag:block ag:transition-all ag:duration-300 ag:delay-100 ag:-translate-y-2 ag:opacity-100" : "ag:height-0 ag:translate-y-0 ag:overflow-hidden ag:opacity-0"}`}
    >
      <div className="ag:right-0 ag:left-0 ag:z-50 ag:absolute ag:bg-white ag:rounded-lg ag:w-full ag:overflow-y-auto ag:-translate-y-full cscs-agent-sender-header-panel">
        <div className="ag:flex ag:justify-between ag:items-center ag:px-4 ag:py-3 ag:w-full">
          <div className="ag:font-bold ag:text-black-85 ag:text-md">{title}</div>
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined className="presets:text-black-85" />}
            onClick={onClose}
          />
        </div>
        <div className="ag:flex ag:px-4 ag:pt-2 ag:pb-3">
          {Components.map((Component, index) => (
            <Component key={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default HeaderPanel;
