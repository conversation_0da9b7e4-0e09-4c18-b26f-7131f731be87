/**
 * Message Transmitter
 *
 * Message chunks are the basic units that make up message packages, responsible for transmitting specific message content.
 * Each chunk contains a message chunk ID and a data block, which can be plain text, structured data, or other types of data.
 * The number and order of chunks affect the integrity and correctness of messages, so validation is required during parsing.
 *
 * Package is the minimum parsing unit of messages, containing one or more chunks; when the client receives chunks,
 * it first stores chunks in a Buffer until a complete package is received before starting to parse,
 * during parsing it will verify that the number and order of chunks are correct to ensure message integrity and correctness.
 *
 * Package types: text, structured, representing plain text messages and structured data messages respectively.
 * Text and structured type packages contain a message body, and can contain multiple chunks;
 *
 * Text type packages are used to transmit plain text messages, using Markdown format text by default for easy parsing and display,
 * its content is updated in real time, and users can see the gradual generation of messages during reception.
 *
 * Text Package
 * Text type packages are used to transmit plain text messages, using Markdown format text by default for easy parsing and display,
 * its content is updated in real time, and users can see the gradual generation of messages during reception.
 *
 * Structured Package
 * Structured data type packages are mainly used to transmit complex data structures, it is a JSON object,
 * its content is static, and users can only see the complete message after reception is completed.
 */

import {
  EventType,
  HeaderMessage,
  IMessagePackage,
  MessageChunk,
  MessagePackageStatus,
  MessagePackageType,
} from "../types.js";

/**
 * Message Transmitter Class
 *
 * Handles the transmission of messages in chunks and packages through Server-Sent Events (SSE).
 * Manages the state of message transmission including event IDs, package IDs, and chunk IDs.
 */
export class ServerTransmitter {
  // Conversation ID
  private readonly conversationId: string;

  // Message ID
  private readonly messageId: string;

  // Agent code/ID
  private readonly agentCode?: string;

  // Event ID
  private eventId: number = -1;

  // Event type: 0: start, 1: loading, 2: end, 3: error
  private eventType: number = EventType.Start;

  // Current sending message package ID, starting from 0
  private packageId: number = -1;

  // Current sending message package type, 0: text, 1: structured
  private packageType: number | null = null;

  // Current sending message package chunk ID, starting from 0
  private chunkId: number = -1;

  // Current sending message package chunk collection
  private chunks: MessageChunk[] = [];

  /**
   * Initialize the Transmitter
   *
   * @param conversationId - The conversation ID
   * @param messageId - The message ID
   * @param agentCode - Optional agent code/ID
   */
  constructor(conversationId: string, messageId: string, agentCode?: string) {
    this.conversationId = conversationId;
    this.messageId = messageId;
    this.agentCode = agentCode;
  }

  /**
   * Start sending message packages
   *
   * Initializes the transmission state and sends a header message as the first structured package.
   *
   * @returns Formatted SSE event string
   */
  public start(): string {
    this.eventId = 0;
    this.eventType = EventType.Start;
    this.packageId = 0;
    this.chunkId = 0;
    this.packageType = MessagePackageType.Structured;

    const headerMsg: HeaderMessage = {
      conversation_id: this.conversationId,
      message_id: this.messageId,
      type: "header",
    };

    const headerStr = JSON.stringify(headerMsg);
    const chunk = this.createChunk(headerStr, true);
    return this.wrapEvent(chunk);
  }

  /**
   * Send message package
   *
   * @param data - Message content
   * @param options - Transmission options
   * @param options.packageType - Message package type
   * @param options.isLast - Whether this is the last message package (default: false)
   * @param options.isNewPackage - Whether this is a new message package (default: false)
   * @returns Formatted SSE event string
   */
  public sendMessage(
    data: string,
    options: {
      packageType: number;
      isLast?: boolean;
      isNewPackage?: boolean;
    },
  ): string {
    const { packageType, isLast = false, isNewPackage = false } = options;

    // Increment event ID and set to loading type
    this.eventId += 1;
    this.eventType = EventType.Loading;

    if (packageType !== null) {
      this.packageType = packageType;
    }

    if (isNewPackage === true) {
      // Reset package ID and chunk ID counters for new message package
      this.packageId += 1;
      this.chunkId = 0;
    } else {
      // Within the same message package, increment chunk ID
      this.chunkId += 1;
    }

    // Create and serialize message chunk
    const chunk = this.createChunk(data, isLast);

    // Wrap as SSE event format
    return this.wrapEvent(chunk);
  }

  /**
   * Send error message package
   *
   * @param data - Error message content
   * @returns Formatted SSE event string
   */
  public sendError(data: string): string {
    this.eventId += 1;
    this.eventType = EventType.Error;
    this.packageId += 1;
    this.chunkId = 0;
    this.packageType = MessagePackageType.Error;

    const chunk = this.createChunk(data, true);
    return this.wrapEvent(chunk);
  }

  /**
   * End message stream transmission
   *
   * Generates an end event to mark message transmission completion.
   * The end event uses PACKAGE_TYPE_NONE type, indicating no more new message packages.
   *
   * Note: This method excludes database saving functionality as requested.
   *
   * @returns Formatted SSE end event
   */
  public end(): string {
    // Increment event ID and set to end type
    this.eventId += 1;
    this.eventType = EventType.End;
    // Create new empty package to mark end
    this.packageId += 1;
    this.chunkId = 0;
    this.packageType = MessagePackageType.None;

    // Create a final chunk with empty data
    const chunk = this.createChunk(null, true);
    return this.wrapEvent(chunk);
  }

  /**
   * Create message chunk
   *
   * Creates a new message chunk based on current state information for data transmission.
   *
   * @param data - Data content to transmit, can be string or structured data
   * @param isLast - Boolean value marking whether this is the last chunk of the current message package
   * @returns Message chunk serialized as JSON string
   */
  private createChunk(data: string | null, isLast: boolean = false): string {
    // Create message chunk object containing metadata
    // TODO: Optimize Chunk type to be consistent with MessagePackage
    const chunk: MessageChunk = {
      event_id: this.eventId, // Event ID for client to identify event order
      event_type: this.eventType, // Event type: start, loading, end, error
      package_id: this.packageId, // Message package ID for merging related chunks
      package_type: this.packageType!, // Package type: text or structured data
      chunk_id: this.chunkId, // Chunk ID to ensure correct chunk order
      is_last: isLast, // Mark whether this is the last chunk in the package
      data: data || "", // Actual transmitted data content
    };

    // Add current chunk to chunk history for subsequent merging and persistence
    this.chunks.push(chunk);
    // Return serialized JSON string
    return JSON.stringify(chunk);
  }

  /**
   * Wrap data as SSE event format
   *
   * @param data - Data to wrap
   * @returns SSE formatted string
   */
  private wrapEvent(data: string): string {
    return "data: " + data + "\n\n";
  }

  /**
   * Get message packages by merging chunks with the same package_id
   *
   * @returns Array of merged message packages
   */
  public getPackages(): IMessagePackage[] {
    const packages: IMessagePackage[] = [];
    let currentPackageId = -1;

    for (const chunk of this.chunks) {
      if (chunk.package_id === currentPackageId) {
        // If it's the same package, directly append data
        packages[packages.length - 1].data += chunk.data;
      } else {
        // If it's not the same package, create a new package
        const messagePackage: IMessagePackage = {
          package_id: chunk.package_id,
          package_type: chunk.package_type,
          status: MessagePackageStatus.Finished,
          data: chunk.data || "",
        };

        currentPackageId = chunk.package_id;
        packages.push(messagePackage);
      }
    }

    return packages;
  }

  // Getter methods for accessing private properties
  public getConversationId(): string {
    return this.conversationId;
  }

  public getMessageId(): string {
    return this.messageId;
  }

  public getAgentCode(): string | undefined {
    return this.agentCode;
  }

  public getChunks(): MessageChunk[] {
    return [...this.chunks]; // Return a copy to prevent external modification
  }
}
