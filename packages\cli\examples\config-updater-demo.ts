/**
 * Demo script showing the LLM-based ConfigUpdater using @openai/agents
 */

/// <reference types="node" />
import type { WidgetGenerationOptions } from "../src/types.js";
import { ConfigUpdater } from "../src/utils/config-updater.js";

// Sample existing configuration
const sampleConfig = `import type { AgentChatConfig } from "@cscs-agent/core";
import Copy from "./widgets/copy";
import Rating from "./widgets/rating";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "问答助手",
      code: "dynamic-page-qa",
      logo: "/assets/dynamic-page-qa-logo.png",
      welcome: "Hi，欢迎使用问答助手",
      description: "面向动态页面管理场景，提供自然语言交互式解答",
      message: {
        blocks: {
          widgets: [],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: "AI",
              },
              {
                code: "Rating",
                component: Rating,
                role: "AI",
              },
            ],
          },
        },
      },
      sender: {
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

async function demonstrateConfigUpdate() {
  console.log("🚀 ConfigUpdater LLM Demo");
  console.log("========================\n");

  const configUpdater = new ConfigUpdater();

  // Example widget options
  const widgetOptions: WidgetGenerationOptions = {
    name: "CustomButton",
    type: "basic",
    targetPath: "/demo/path",
    description: "A custom interactive button widget",
    placement: "message",
    slot: "blocks",
  };

  console.log("📝 Original Configuration:");
  console.log("---------------------------");
  console.log(sampleConfig);
  console.log("\n");

  console.log("🔧 Widget to Add:");
  console.log("------------------");
  console.log(`Name: ${widgetOptions.name}`);
  console.log(`Type: ${widgetOptions.type}`);
  console.log(`Placement: ${widgetOptions.placement}`);
  console.log(`Slot: ${widgetOptions.slot}`);
  console.log(`Description: ${widgetOptions.description}`);
  console.log("\n");

  try {
    // Demonstrate the LLM prompt building
    const prompt = (configUpdater as any).buildConfigUpdatePrompt(
      sampleConfig,
      widgetOptions,
      "./widgets/custombutton",
      "@Custom/CustomButton",
    );

    console.log("🤖 LLM Prompt:");
    console.log("---------------");
    console.log(prompt);
    console.log("\n");

    console.log("✅ Demo completed successfully!");
    console.log("The ConfigUpdater now uses @openai/agents for LLM-based configuration updates.");
    console.log("Benefits:");
    console.log("- Uses @openai/agents package for robust LLM integration");
    console.log("- Understands complex configuration structures");
    console.log("- Maintains existing formatting and style");
    console.log("- Handles edge cases better than regex-based approaches");
    console.log("- Direct LLM integration without manual fallback methods");
  } catch (error) {
    console.error("❌ Demo failed:", error);
  }
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateConfigUpdate().catch(console.error);
}

// Also run if called with node
if (process.argv[1] && process.argv[1].includes("config-updater-demo")) {
  demonstrateConfigUpdate().catch(console.error);
}

export { demonstrateConfigUpdate };
