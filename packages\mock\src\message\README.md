# TypeScript Message Transmitter

This directory contains the TypeScript conversion of the Python Transmitter class from `core/message/transmitter.py`.

## Files

- **`message/types.ts`** - Contains all type definitions, interfaces, and enums
- **`message/transmitter.ts`** - Main Transmitter class implementation
- **`message/index.ts`** - Module exports

## Key Features

### Complete Type Safety
- All class properties, method parameters, and return types are properly typed
- TypeScript enums for all constant values
- Interfaces for all data structures

### Converted Elements

#### Enums
- `EventType` - Event type constants (START, LOADING, END, ERROR)
- `PackageType` - Package type constants (NONE, TEXT, STRUCTURED, THINKING, ERROR)
- `StructuredChunkType` - Structured chunk types (HEADER, COMMAND)
- `MessagePackageStatus` - Message status (LOADING, FINISHED, ERROR)
- `MessageType` - Message role types (HUMAN, AI, SYSTEM)

#### Interfaces
- `Chunk` - Message chunk data structure
- `MessagePackage` - Message package data structure
- `Command` - Command structure for structured messages
- `CommandMessage` - Command message structure
- `HeaderMessage` - Header message structure

#### Main Class
- `Transmitter` - Complete message transmission functionality

### Naming Conventions
- Converted Python snake_case to TypeScript camelCase
- Used proper TypeScript access modifiers (public/private/protected)
- Added JSDoc comments for all public methods

### Excluded Features
As requested, the following were excluded from the conversion:
- `save_packages` method (database-related)
- Database connection/session handling code
- All database-related imports and dependencies

## Usage Example

```typescript
import { Transmitter, PackageType, EventType } from './message';

// Create a new transmitter instance
const transmitter = new Transmitter('conversation-123', 'message-456', 'agent-789');

// Start transmission
const startEvent = transmitter.start();
console.log(startEvent); // SSE formatted start event

// Send a text message
const textEvent = transmitter.sendMessage('Hello, world!', {
  packageType: PackageType.TEXT,
  isLast: true,
  isNewPackage: true
});
console.log(textEvent); // SSE formatted message event

// End transmission
const endEvent = transmitter.end();
console.log(endEvent); // SSE formatted end event

// Get all packages
const packages = transmitter.getPackages();
console.log(packages); // Array of MessagePackage objects
```

## Type Safety Benefits

The TypeScript version provides:
- Compile-time type checking
- IntelliSense support in IDEs
- Better refactoring capabilities
- Clear API contracts through interfaces
- Reduced runtime errors through static typing

## Architecture

The converted code maintains the same structure and method organization as the original Python implementation while leveraging TypeScript's type system for better developer experience and code reliability.
