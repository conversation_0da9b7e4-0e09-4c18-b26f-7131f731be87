# ConfigUpdater Diff Functionality

The ConfigUpdater class now includes a comprehensive diff comparison feature that provides transparency and control when modifying configuration files.

## Overview

After the LLM generates updated configuration content, the system automatically:

1. **Captures Original Content**: Stores the original file content before any modifications
2. **Generates Diff**: Compares original vs. modified content using a line-by-line diff algorithm
3. **Displays Changes**: Shows differences in a clear, git-like format
4. **Prompts User**: Offers options to confirm, revert, or view full diff
5. **Applies Choice**: Executes the user's decision (save changes or keep original)

## Features

### Visual Diff Display

The diff output shows:
- **Added lines**: Marked with `+` in green
- **Removed lines**: Marked with `-` in red  
- **Context lines**: Unmarked lines in gray for reference
- **File path**: Clear indication of which file is being modified
- **Change summary**: Header showing original vs modified

### User Options

When changes are detected, users can choose:

1. **Confirm and apply changes**: Save the modified content to the file
2. **Revert and keep original**: Discard changes and preserve original file
3. **View full diff again**: Display complete diff with all lines for detailed review

### Safety Features

- **Backup Creation**: Automatic backup of original file before any changes
- **No Auto-Apply**: Changes are never applied without explicit user confirmation
- **Change Detection**: Only prompts user when actual differences are found
- **Rollback Support**: Easy reversion to original content if needed

## Example Output

```
--- /path/to/agent-config.tsx (original)
+++ /path/to/agent-config.tsx (modified)
@@ Changes @@
  import type { AgentChatConfig } from "@cscs-agent/core";
+ import NewWidget from "./widgets/new-widget";

  export const config: AgentChatConfig = {
    agents: [
      {
        name: "Test Agent",
        message: {
          blocks: {
            widgets: [
+             {
+               code: "@Custom/NewWidget",
+               component: NewWidget,
+             }
            ],
          },
        },
      },
    ],
  };

? Review the changes above. What would you like to do?
  ❯ Confirm and apply changes
    Revert and keep original file  
    View full diff again
```

## Implementation Details

### Diff Algorithm

The system uses a simple but effective line-by-line comparison algorithm that:
- Compares each line of original vs modified content
- Identifies additions, deletions, and unchanged context
- Maintains line number references for accurate reporting
- Handles edge cases like empty files or identical content

### Integration Points

The diff functionality is seamlessly integrated into the existing ConfigUpdater workflow:

```typescript
// Existing workflow enhanced with diff
const originalContent = await this.fsManager.readFile(configPath);
const updatedContent = await this.updateExistingAgentConfigWithLLM(...);

// New diff functionality
const diffResult = this.generateDiff(configPath, originalContent, updatedContent);

if (diffResult.hasChanges) {
  const userChoice = await this.promptUserForConfirmation(diffResult);
  await this.handleUserChoice(configPath, originalContent, updatedContent, userChoice);
}
```

### Testing

Comprehensive test coverage includes:
- Diff generation with various content changes
- User confirmation workflows (confirm/revert)
- No-change detection scenarios
- Error handling and edge cases
- Mock interactions with inquirer prompts

## Benefits

1. **Transparency**: Users see exactly what will change before it happens
2. **Safety**: Built-in safeguards prevent unwanted modifications
3. **Control**: User maintains full control over when changes are applied
4. **Confidence**: Clear visual feedback builds trust in the system
5. **Reversibility**: Easy rollback if changes aren't desired

## Backward Compatibility

The diff functionality is fully backward compatible:
- Existing ConfigUpdater API remains unchanged
- New file creation workflow is unaffected
- All existing functionality continues to work as before
- Only the update workflow for existing files includes the new diff step

This enhancement significantly improves the user experience by providing transparency and control over configuration file modifications while maintaining the existing API and functionality.
