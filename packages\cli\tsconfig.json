{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./lib", "rootDir": "./src", "resolveJsonModule": true, "types": ["node"], "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "templates"]}