import { XMLParser, XMLValidator } from "fast-xml-parser";
import React, { useMemo } from "react";

import { EmbeddedItem, EmbeddedItemType, EmbeddedStateItem, EmbeddedWidgetItem } from "@/types";

import State from "./State";
import WidgetRender from "./Widget";

interface EmbeddedBlockProps {
  content: string;
}

const arrayTags = ["set", "widget", "state"];

const EmbeddedBlock: React.FC<EmbeddedBlockProps> = (props) => {
  const { content } = props;

  const parsedEmbeddedItems: EmbeddedItem[] = useMemo(() => {
    const xmlStr = content;
    const isValid = XMLValidator.validate(xmlStr);
    if (!isValid) {
      console.error("Widget parser error, Invalid XML:", xmlStr);
      return [];
    }

    const parser = new XMLParser({
      isArray: (tagName) => {
        return arrayTags.includes(tagName);
      },
      trimValues: false,
    });
    const result = parser.parse(xmlStr);
    let parsedWidgets: EmbeddedWidgetItem[] = [];
    let parsedState: EmbeddedStateItem[] = [];

    if (Array.isArray(result.widget)) {
      parsedWidgets = (result.widget as any[]).map((item, index) => ({
        key: `widget-${index}`,
        type: EmbeddedItemType.Widget,
        ...item,
      }));
    }

    if (Array.isArray(result.state)) {
      parsedState = (result.state as any[]).map((item, index) => ({
        key: `state-${index}`,
        type: EmbeddedItemType.State,
        ...item,
      }));
    }
    // 解析json格式的值
    parsedState = parsedState.map((i) => {
      const setList = i.set.map((j) => {
        let value = j.value;

        if (typeof value === "string") {
          try {
            value = JSON.parse(value);
          } catch {}
        }

        return {
          ...j,
          value,
        };
      });
      return {
        key: i.key,
        type: i.type,
        set: setList,
      };
    }) as any[];

    return [...parsedWidgets, ...parsedState];
  }, [content]);

  return parsedEmbeddedItems?.map((i) => {
    if (i.type === EmbeddedItemType.Widget) {
      const item = i as EmbeddedWidgetItem;
      return <WidgetRender key={item.key} config={item} />;
    }
    if (i.type === EmbeddedItemType.State) {
      const item = i as EmbeddedStateItem;
      return <State key={item.key} setList={item.set} />;
    }
  });
};

export default EmbeddedBlock;
