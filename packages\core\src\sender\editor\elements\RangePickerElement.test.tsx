import { describe, expect, it, vi } from "vitest";

import { render, screen } from "@testing-library/react";

import { RangePickerElement } from "./RangePickerElement";

// Mock Ant Design components
vi.mock("antd", () => ({
  DatePicker: {
    RangePicker: ({ placeholder, disabled }: any) => (
      <input 
        data-testid="rangepicker" 
        placeholder={Array.isArray(placeholder) ? placeholder.join(",") : placeholder} 
        disabled={disabled} 
      />
    ),
  },
  Tooltip: ({ children, title }: any) => (
    <div data-testid="tooltip" title={title}>
      {children}
    </div>
  ),
}));

// Mock slate-react
vi.mock("slate-react", () => ({
  RenderElementProps: {},
}));

// Mock dayjs
vi.mock("dayjs", () => ({
  default: vi.fn(() => null),
}));

describe("RangePickerElement", () => {
  const mockElement = {
    placeholder: ["开始日期", "结束日期"],
    defaultValue: ["", ""],
    tooltips: "选择日期范围",
    disabled: false,
    format: "YYYY-MM-DD",
    showTime: false,
    allowClear: true,
    separator: "~",
  } as any;

  it("renders without crashing", () => {
    render(<RangePickerElement element={mockElement} {...({} as any)} />);

    const rangePicker = screen.getByTestId("rangepicker");
    expect(rangePicker).toBeTruthy();
  });

  it("renders with correct placeholder", () => {
    render(<RangePickerElement element={mockElement} {...({} as any)} />);

    const rangePicker = screen.getByTestId("rangepicker");
    expect(rangePicker.getAttribute("placeholder")).toBe("开始日期,结束日期");
  });

  it("renders with disabled state", () => {
    const disabledElement = { ...mockElement, disabled: true };
    render(<RangePickerElement element={disabledElement} {...({} as any)} />);

    const rangePicker = screen.getByTestId("rangepicker");
    expect(rangePicker.getAttribute("disabled")).toBe("");
  });

  it("renders with tooltip", () => {
    render(<RangePickerElement element={mockElement} {...({} as any)} />);

    const tooltip = screen.getByTestId("tooltip");
    expect(tooltip.getAttribute("title")).toBe("选择日期范围");
  });
});
