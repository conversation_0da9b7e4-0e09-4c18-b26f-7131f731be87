# LLM-Based ConfigUpdater

## Overview

The ConfigUpdater has been enhanced to use the @openai/agents package for intelligently reading and modifying agent configuration files. This approach provides more robust and flexible configuration updates compared to the previous regex-based method.

## Key Features

### 🤖 LLM-Powered Updates
- Uses @openai/agents package for robust LLM integration
- Maintains existing code structure, formatting, and style
- Handles complex nested configurations intelligently
- Preserves comments and existing imports

### 🚀 Direct LLM Integration
- No manual fallback methods - uses LLM directly for all configuration updates
- Follows existing CLI architectural patterns with @openai/agents
- Improved error handling and validation

### 🎯 Smart Configuration Understanding
- Understands TypeScript/JavaScript syntax
- Recognizes CSCS Agent configuration patterns
- Properly places widgets in correct sections based on placement and slot
- Maintains type safety and import statements

## Configuration

### Environment Variables

```bash
# OpenAI API Configuration
OPENAI_API_KEY=your-api-key-here
OPENAI_MODEL=gpt-4o  # Optional, defaults to gpt-4o
```

### Default Configuration

The system uses the @openai/agents package with:
- Model: `gpt-4o` (configurable via OPENAI_MODEL environment variable)
- Agent instructions optimized for CSCS Agent configuration files
- Built-in error handling and validation

## Usage

### Basic Usage

```typescript
import { ConfigUpdater } from '@cscs-agent/cli/utils/config-updater';

const configUpdater = new ConfigUpdater();

await configUpdater.updateAgentConfig(
  '/path/to/agent-config.tsx',
  {
    name: 'MyWidget',
    type: 'button',
    targetPath: '/project/path',
    placement: 'message',
    slot: 'blocks',
    description: 'A custom button widget'
  },
  './widgets/mywidget',
  '@Custom/MyWidget'
);
```

### Widget Placement Options

#### Message Placement
```typescript
// Adds to message.blocks.widgets
{ placement: 'message', slot: 'blocks' }

// Adds to message.slots.footer.widgets
{ placement: 'message', slot: 'footer' }

// Adds to message.slots.header.widgets
{ placement: 'message', slot: 'header' }
```

#### Sender Placement
```typescript
// Adds to sender.slots.footer.widgets
{ placement: 'sender', slot: 'footer' }

// Adds to sender.slots.headerPanel.widgets
{ placement: 'sender', slot: 'headerPanel' }
```

#### Side Panel Placement
```typescript
// Adds to sidePanel.render.widgets
{ placement: 'sidePanel' }
```

## LLM Prompt Structure

The LLM receives a detailed prompt that includes:

1. **Current Configuration**: Complete existing configuration file content
2. **Widget Details**: Name, import path, code, placement, slot, description
3. **Requirements**: Specific instructions for maintaining structure and style
4. **Format Guidelines**: Expected widget configuration format
5. **Placement Rules**: Where to add the widget based on placement and slot

## Error Handling

### LLM Integration Failures
- Network errors and API failures are properly caught and logged
- Invalid responses are validated and rejected
- Clear error messages for debugging

### Configuration Validation
- Validates that updated configuration contains required imports
- Ensures export statements are present
- Checks for basic TypeScript syntax correctness
- Throws descriptive errors for invalid LLM responses

### Logging
- Detailed logging for debugging LLM interactions
- Clear indication when fallback methods are used
- Warning messages for configuration issues

## Testing

### Unit Tests
```bash
cd packages/cli
npm run test
```

### Demo Script
```bash
cd packages/cli
npx tsx examples/config-updater-demo.ts
```

## Benefits Over Previous Approach

### 🎯 Accuracy
- Better understanding of code structure
- Handles edge cases and complex configurations
- Maintains proper indentation and formatting

### 🔧 Flexibility
- Adapts to different configuration styles
- Handles custom modifications and extensions
- Works with various TypeScript patterns

### 🛡️ Reliability
- Fallback mechanism ensures no breaking changes
- Comprehensive error handling
- Maintains backward compatibility

### 🚀 Future-Proof
- Easy to extend with new widget types
- Adaptable to configuration schema changes
- Supports complex placement scenarios

## Migration Notes

### Existing Projects
- No changes required for existing projects
- Automatic detection and use of LLM when available
- Seamless fallback to previous behavior

### API Compatibility
- All existing methods remain unchanged
- Same input parameters and return types
- Drop-in replacement for previous ConfigUpdater

## Troubleshooting

### Common Issues

1. **LLM API Not Available**
   - Check network connectivity
   - Verify API key and base URL
   - System automatically falls back to manual updates

2. **Invalid Configuration Generated**
   - LLM response validation catches most issues
   - Automatic fallback to manual updates
   - Check logs for detailed error information

3. **Performance Concerns**
   - LLM calls are cached where possible
   - Timeout settings prevent hanging
   - Fallback ensures quick recovery

### Debug Mode

Enable detailed logging:
```bash
DEBUG=config-updater npm run generate
```

## Future Enhancements

- Configuration caching for improved performance
- Support for custom LLM models and providers
- Advanced validation and error recovery
- Integration with code formatting tools
- Support for multiple configuration file formats
