import { But<PERSON>, Card, Form, Input, Typography, message } from "antd";
import JSEncrypt from "jsencrypt";
import { useState } from "react";
import { useNavigate } from "react-router";

import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { RequestError, post } from "@cscs-agent/core";

const { Title } = Typography;

interface LoginFormValues {
  username: string;
  password: string;
  remember: boolean;
}

const publicKey =
  "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCI+jAqFHl+GBGhhzjIJdHAyDv2heJIS2dw8+vR0sQGnLoehNV/YRtVocpdnWOcXWOr/cIJYG/e9gKSE8nnLxNMHAJXlFeA4gTT+HIOyRhcmj7Jzon7Jfv/Ys1yLAnL1tDPIWRGhcu0YnqhOysQiQPZ8tKFYTc3xgtzcy80HwcjRQIDAQAB";

const encrypt = (msg: string) => {
  const jsEncrypt = new JSEncrypt();
  jsEncrypt.setPublicKey(publicKey);
  return jsEncrypt.encrypt(msg);
};

const Login = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: LoginFormValues) => {
    try {
      setLoading(true);
      // Simulate API call
      const result = await post("/login", {
        username: values.username,
        password: encrypt(values.password),
      });

      const accessToken = (result.data as { data: { accessToken: string } }).data?.accessToken;

      if (!accessToken) {
        message.error("登录失败，请检查账号密码");
        return;
      }

      // Show success message
      message.success("登录成功");

      localStorage.setItem("_token", JSON.stringify(accessToken));

      // Navigate to home page after successful login
      navigate("/");
    } catch (error) {
      if ((error as RequestError).status === 401) {
        message.error("登录失败，请检查账号密码");
      } else {
        console.error("Login error:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pts:flex pts:justify-center pts:items-center pts:bg-gray-50 pts:min-h-screen">
      <Card className="pts:shadow-lg pts:w-full pts:max-w-md" variant="borderless">
        <div className="pts:mb-6 pts:text-center">
          <Title level={2} className="pts:mb-1">
            登录
          </Title>
        </div>

        <Form name="login-form" initialValues={{ remember: true }} onFinish={onFinish} size="large" layout="vertical">
          <Form.Item name="username" rules={[{ required: true, message: "请输入账号" }]}>
            <Input prefix={<UserOutlined className="pts:text-gray-400" />} placeholder="账号" />
          </Form.Item>

          <Form.Item name="password" rules={[{ required: true, message: "请输入密码" }]}>
            <Input.Password prefix={<LockOutlined className="pts:text-gray-400" />} placeholder="密码" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" className="pts:w-full" loading={loading}>
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;
