/**
 * Agent configuration update utilities using LLM
 */

import chalk from "chalk";
import { diffLines, type Change } from "diff";
import inquirer from "inquirer";
import { resolve } from "path";

import { Agent, run } from "@openai/agents";

import type { DiffLine, DiffResult, UserC<PERSON>ice, WidgetGenerationOptions } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class ConfigUpdater {
  private fsManager: FileSystemManager;
  private agent: Agent;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Initialize the LLM agent for configuration updates
    this.agent = new Agent({
      name: "ConfigUpdater",
      instructions: `You are an expert TypeScript developer specializing in CSCS Agent configuration files.
        Your task is to read and modify agent configuration files to include new widget components.

        Always:
        - Maintain existing code structure, formatting, and style
        - Preserve all existing imports, configurations, and widgets
        - Add import statements for new components at the top with other imports
        - Place widgets in the correct sections based on placement and slot
        - Follow TypeScript best practices and ensure syntax correctness
        - Return ONLY the complete updated configuration file content without explanations`,
      model: process.env.OPENAI_MODEL,
    });
  }

  /**
   * Update agent configuration to include new widget using LLM
   */
  async updateAgentConfig(
    configPath: string,
    options: WidgetGenerationOptions,
    widgetImportPath: string,
    widgetCode: string,
  ): Promise<void> {
    if (!this.fsManager.fileExists(configPath)) {
      // Create new agent config file
      const newConfigContent = this.generateNewAgentConfig(options, widgetImportPath, widgetCode);
      await this.fsManager.writeFile(configPath, newConfigContent);
      Logger.info("Created new agent-config.tsx file");
    } else {
      // Update existing agent config file using LLM with diff comparison
      const originalContent = await this.fsManager.readFile(configPath);

      try {
        const updatedContent = await this.updateExistingAgentConfigWithLLM(
          originalContent,
          options,
          widgetImportPath,
          widgetCode,
        );

        // Generate and display diff, then prompt user for confirmation
        const diffResult = this.generateDiff(configPath, originalContent, updatedContent);

        if (diffResult.hasChanges) {
          const userChoice = await this.promptUserForConfirmation(diffResult);
          await this.handleUserChoice(configPath, originalContent, updatedContent, userChoice, options.name);
        } else {
          Logger.info("No changes detected in the configuration file");
        }
      } catch (error) {
        Logger.error(`LLM config update failed: ${error instanceof Error ? error.message : "Unknown error"}`);
        throw error;
      }
    }
  }

  /**
   * Generate new agent config content
   */
  private generateNewAgentConfig(options: WidgetGenerationOptions, importPath: string, widgetCode: string): string {
    const componentName = options.name;

    return `import type { AgentChatConfig } from "@cscs-agent/core";
import ${componentName} from "${importPath}";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Generated Agent",
      code: "generated-agent",
      description: "Agent with generated widgets",
      ${this.generateWidgetPlacement(options, componentName, widgetCode)}
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};
`;
  }

  /**
   * Update existing agent config content using LLM
   */
  private async updateExistingAgentConfigWithLLM(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): Promise<string> {
    const prompt = this.buildConfigUpdatePrompt(content, options, importPath, widgetCode);

    const result = await run(this.agent, prompt);

    if (!result.finalOutput || typeof result.finalOutput !== "string") {
      throw new Error("LLM did not return valid configuration content");
    }

    return this.extractConfigFromResponse(result.finalOutput);
  }

  /**
   * Generate widget placement configuration
   */
  private generateWidgetPlacement(options: WidgetGenerationOptions, componentName: string, widgetCode: string): string {
    const widgetConfig = `{
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return `message: {
        blocks: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
      } else {
        return `message: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
      }
    } else if (options.placement === "sender") {
      return `sender: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
    } else if (options.placement === "sidePanel") {
      return `sidePanel: {
        render: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
    }

    return "";
  }

  /**
   * Build prompt for LLM to update configuration
   */
  private buildConfigUpdatePrompt(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): string {
    return `You are an expert TypeScript developer working with CSCS Agent configuration files.

**Task**: Update the existing agent configuration file to include a new widget component.

**Current Configuration File Content**:
\`\`\`typescript
${content}
\`\`\`

**Widget Details**:
- Component Name: ${options.name}
- Import Path: ${importPath}
- Widget Code: ${widgetCode}
- Placement: ${options.placement}
- Slot: ${options.slot || "blocks"}
- Description: ${options.description || "A custom widget component"}

**Requirements**:
1. Add the import statement for the new component at the top with other imports
2. Add the widget configuration to the appropriate section based on placement and slot:
   - For placement "message" and slot "blocks": add to message.blocks.widgets array
   - For placement "message" and other slots: add to message.slots.{slot}.widgets array
   - For placement "sender": add to sender.slots.{slot}.widgets array
   - For placement "sidePanel": add to sidePanel.render.widgets array
3. Maintain the existing structure and formatting
4. Preserve all existing imports, configurations, and widgets
5. Follow the existing code style and indentation
6. Ensure the TypeScript syntax is correct

**Widget Configuration Format**:
\`\`\`typescript
{
  code: "${widgetCode}",
  component: ${options.name},
}
\`\`\`

**Important**: Return ONLY the complete updated configuration file content. Do not include any explanations or markdown code blocks.`;
  }

  /**
   * Extract configuration content from LLM response
   */
  private extractConfigFromResponse(response: string): string {
    let config = response.trim();

    // Remove markdown code blocks if present
    config = config.replace(/^```(?:typescript|tsx|ts|javascript|jsx|js)?\n/, "");
    config = config.replace(/\n```$/, "");

    // Ensure the config contains required elements
    if (!config.includes("import") || !config.includes("export")) {
      throw new Error("Generated configuration appears to be incomplete");
    }

    return config;
  }

  /**
   * Generate diff between original and modified content using diff package
   */
  private generateDiff(filePath: string, originalContent: string, modifiedContent: string): DiffResult {
    const changes = diffLines(originalContent, modifiedContent);
    const resultDiffLines: DiffLine[] = [];
    let lineNumber = 1;

    for (const change of changes) {
      const lines = change.value.split("\n");
      // Remove the last empty line if it exists (from split)
      if (lines[lines.length - 1] === "") {
        lines.pop();
      }

      for (const line of lines) {
        if (change.added) {
          resultDiffLines.push({
            type: "added",
            content: line,
            lineNumber: lineNumber,
          });
        } else if (change.removed) {
          resultDiffLines.push({
            type: "removed",
            content: line,
            lineNumber: lineNumber,
          });
        } else {
          resultDiffLines.push({
            type: "context",
            content: line,
            lineNumber: lineNumber,
          });
        }
        lineNumber++;
      }
    }

    return {
      filePath,
      originalContent,
      modifiedContent,
      diffLines: resultDiffLines,
      hasChanges: changes.some((change: Change) => change.added || change.removed),
    };
  }

  /**
   * Display diff in a git-like format
   */
  private displayDiff(diffResult: DiffResult): void {
    console.log(chalk.bold(`\n--- ${diffResult.filePath} (original)`));
    console.log(chalk.bold(`+++ ${diffResult.filePath} (modified)`));
    console.log(chalk.gray("@@ Changes @@"));

    for (const line of diffResult.diffLines) {
      switch (line.type) {
        case "added":
          console.log(chalk.green(`+ ${line.content}`));
          break;
        case "removed":
          console.log(chalk.red(`- ${line.content}`));
          break;
        case "context":
          // Only show context lines near changes for brevity
          if (this.isNearChange(line, diffResult.diffLines)) {
            console.log(chalk.gray(`  ${line.content}`));
          }
          break;
      }
    }
    console.log();
  }

  /**
   * Check if a context line is near a change (for display purposes)
   */
  private isNearChange(contextLine: DiffLine, allLines: DiffLine[]): boolean {
    const index = allLines.indexOf(contextLine);
    const contextRange = 2;

    for (let i = Math.max(0, index - contextRange); i <= Math.min(allLines.length - 1, index + contextRange); i++) {
      if (allLines[i].type !== "context") {
        return true;
      }
    }
    return false;
  }

  /**
   * Prompt user for confirmation after showing diff
   */
  private async promptUserForConfirmation(diffResult: DiffResult): Promise<UserChoice> {
    this.displayDiff(diffResult);

    const { choice } = await inquirer.prompt([
      {
        type: "list",
        name: "choice",
        message: "Review the changes above. What would you like to do?",
        choices: [
          { name: "Confirm and apply changes", value: "confirm" },
          { name: "Revert and keep original file", value: "revert" },
          { name: "View full diff again", value: "view" },
        ],
      },
    ]);

    if (choice === "view") {
      // Show full diff and prompt again
      this.displayFullDiff(diffResult);
      return this.promptUserForConfirmation(diffResult);
    }

    return choice as UserChoice;
  }

  /**
   * Display full diff with all lines
   */
  private displayFullDiff(diffResult: DiffResult): void {
    console.log(chalk.bold("\n=== FULL DIFF ==="));
    console.log(chalk.bold(`File: ${diffResult.filePath}`));
    console.log(chalk.gray("=".repeat(50)));

    for (const line of diffResult.diffLines) {
      switch (line.type) {
        case "added":
          console.log(chalk.green(`+ ${line.content}`));
          break;
        case "removed":
          console.log(chalk.red(`- ${line.content}`));
          break;
        case "context":
          console.log(chalk.gray(`  ${line.content}`));
          break;
      }
    }
    console.log(chalk.gray("=".repeat(50)));
  }

  /**
   * Handle user's choice after diff review
   */
  private async handleUserChoice(
    configPath: string,
    _originalContent: string,
    modifiedContent: string,
    choice: UserChoice,
    widgetName: string,
  ): Promise<void> {
    const widgetPath = resolve(`./src/widgets/${widgetName.toLocaleLowerCase()}`);

    switch (choice) {
      case "confirm":
        await this.fsManager.updateFile(configPath, modifiedContent, true);
        Logger.info("✅ Changes confirmed and applied to agent-config.tsx");
        break;
      case "revert":
        // File remains unchanged
        // remove widget directory and files
        this.fsManager.deleteDirectory(widgetPath);
        Logger.info("❌ Changes reverted. Original file content preserved.");
        break;
      default:
        Logger.error(`Unknown choice: ${choice}`);
        break;
    }
  }
}
