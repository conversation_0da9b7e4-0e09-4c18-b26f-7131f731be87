import { DatePicker as AntdDatePicker, Tooltip } from "antd";
import dayjs, { Dayjs } from "dayjs";
import React, { useState } from "react";
import { RenderElementProps } from "slate-react";

export interface DatePickerElementProps {
  placeholder?: string;
  defaultValue?: string;
  tooltips?: string;
  disabled?: boolean;
  format?: string;
  showTime?: boolean;
  allowClear?: boolean;
}

// DatePicker Element Component
export const DatePickerElement: React.FC<RenderElementProps> = ({ element }) => {
  const {
    placeholder,
    defaultValue,
    tooltips,
    disabled,
    format = "YYYY-MM-DD",
    showTime = false,
    allowClear = true,
  } = element as DatePickerElementProps;

  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(defaultValue ? dayjs(defaultValue) : null);

  const handleChange = (date: Dayjs | null) => {
    setSelectedDate(date);
    // Update the element's defaultValue to persist the selection
    (element.defaultValue as any) = date ? date.format(format) : "";
  };

  return (
    <span contentEditable={false} className="ag:px-1">
      <Tooltip title={tooltips}>
        <AntdDatePicker
          value={selectedDate}
          onChange={handleChange}
          disabled={disabled}
          placeholder={placeholder}
          format={format}
          showTime={showTime}
          allowClear={allowClear}
          size="small"
          className="ag:min-w-30"
          variant="filled"
        />
      </Tooltip>
    </span>
  );
};

export default DatePickerElement;
