import { defineMock } from "vite-plugin-mock-dev-server";

import { MessageMocker } from "@cscs-agent/mock";

export default defineMock([
  {
    url: "/api/chat/test",
    method: "POST",
    response: (req, res) => {
      const message = "2、7月15日前，由直接上级对员工第二季度绩效完成情况进行考核、出具考核意见。一级部门负责人审核考核结果并将《2025年第二季度员工绩效考核结果》（附件2）汇总至综合管理部。其中，涉及员工项目工作表现的，请直接上级事先征询所在项目经理意见；直接上级为部门负责人的员工考核结果由综合管理部统一呈报总经理审核。3、7月18日前，由综合管理部汇总员工考核结果报总经理审批。4、员工季度考核结果由综合管理部统一通知。有关员工季度工作表现、绩效改进建议等，请各部门管理人员与员工进行必要的沟通。员工对考核结果有异议的，需在获知结果的5个工作日内向一级部门负责人或综合管理部提出书面申诉并详细说明申诉理由。";
      const mocker = new MessageMocker(req, res, 20);
      mocker.start(message);
    },
  },
]);
