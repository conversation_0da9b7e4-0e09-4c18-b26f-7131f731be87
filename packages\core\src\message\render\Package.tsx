import React, { useEffect, useRef } from "react";

import {
  BlockConfig,
  BlockStatus,
  BlockType,
  IMessagePackage,
  MessagePackageStatus,
  MessagePackageType,
} from "@/types";

import EmbeddedBlock from "./EmbeddedBlock";
import ErrorBlock from "./ErrorBlock";
import TextBlock from "./TextBlock";
import ThinkingBlock from "./ThinkingBlock";

interface PackageRenderProps {
  msgPackage: IMessagePackage;
}

/**
 * 解析文本，拆分纯文本和<message-embedded></message-embedded>标签
 * @param data
 */
function parseBlocks(data: string, status: MessagePackageStatus, loadingBlockStart: number) {
  const blocks: BlockConfig[] = [];
  let lastIndex = -1;
  let match;

  const regex = /<message-embedded>(.*?)<\/message-embedded>/gs;

  // 匹配<message-embedded>标签
  while ((match = regex.exec(data)) !== null) {
    // 将标签之前的文本放置到 TextBlock
    if (match.index > lastIndex) {
      const start = lastIndex + 1;
      const end = match.index - 1;
      // 使用文本的起始位置作为id，避免TextBlock组件重复渲染
      const textBlockId = `text-${loadingBlockStart + start}`;
      blocks.push({
        id: textBlockId,
        type: BlockType.Text,
        content: data.slice(start, end + 1),
        status: BlockStatus.Finished,
        start: loadingBlockStart + start,
        end: loadingBlockStart + end,
      });
    }

    // 使用标签的起始位置作为id，避免WidgetBlock组件重复渲染
    const embeddedBlockId = `embedded-${loadingBlockStart + match.index}`;
    blocks.push({
      id: embeddedBlockId,
      type: BlockType.Embedded,
      content: match[1],
      status: BlockStatus.Finished,
      start: loadingBlockStart + match.index,
      end: loadingBlockStart + match.index + match[0].length - 1,
    });

    lastIndex = match.index + match[0].length - 1;
  }

  // 添加剩余的纯文本
  if (lastIndex < data.length) {
    // 使用文本的起始位置作为id，避免TextBlock组件重复渲染
    const lastTextBlockId = `text-${loadingBlockStart + lastIndex + 1}`;
    blocks.push({
      id: lastTextBlockId,
      type: BlockType.Text,
      content: data.slice(lastIndex + 1),
      status: BlockStatus.Finished,
      start: loadingBlockStart + lastIndex + 1,
      end: loadingBlockStart + data.length - 1,
    });
  }

  // 如果状态是加载中，将最后一个文本块的状态设置为加载中
  if (status === MessagePackageStatus.Loading) {
    const lastBlock = blocks[blocks.length - 1];
    if (lastBlock.type === BlockType.Text) {
      lastBlock.status = BlockStatus.Loading;
    }
  }

  return blocks;
}

const PackageRender: React.FC<PackageRenderProps> = (props) => {
  const { msgPackage } = props;
  const buffer = useRef<string>(""); // 缓冲区，存储未接收完整的内容
  const pointer = useRef(-1); // 指针，指向当前已经处理到的字符位置
  const loadingBlockStart = useRef(0);
  const [blocks, setBlocks] = React.useState<BlockConfig[]>([]); // 块列表
  const timer = useRef<number | null>(null);
  const lastRenderTime = useRef(0);

  const updateBlocks = () => {
    if (msgPackage.package_type === MessagePackageType.Text && typeof msgPackage.data === "string") {
      const content = buffer.current + msgPackage.data.slice(pointer.current + 1);
      pointer.current = msgPackage.data.length - 1;
      loadingBlockStart.current = pointer.current - content.length + 1;
      const newBlocks = parseBlocks(content, msgPackage.status, loadingBlockStart.current);

      setBlocks((prevBlocks) => {
        if (prevBlocks.length > 0 && prevBlocks[prevBlocks.length - 1].status === BlockStatus.Loading) {
          prevBlocks.pop();
        }
        return [...prevBlocks, ...newBlocks];
      });

      // 如果最后一个block是加载中的文本块，将 buffer 替换为这个块的内容
      const lastBlock = newBlocks[newBlocks.length - 1];
      if (lastBlock.status === BlockStatus.Loading && lastBlock.type === BlockType.Text) {
        buffer.current = lastBlock.content;
      }
    }

    if (msgPackage.package_type === MessagePackageType.Thinking) {
      setBlocks((prevBlocks) => {
        if (prevBlocks.length > 0 && prevBlocks[prevBlocks.length - 1].status === BlockStatus.Loading) {
          prevBlocks.pop();
        }
        return [
          ...prevBlocks,
          {
            id: "thinking",
            type: BlockType.Thinking,
            content: msgPackage.data,
            status: msgPackage.status === MessagePackageStatus.Loading ? BlockStatus.Loading : BlockStatus.Finished,
            start: 0,
            end: msgPackage.data.length - 1,
          },
        ];
      });
    }

    // Error Message Package 是结构化数据且只有一个Chunk
    if (msgPackage.package_type === MessagePackageType.Error) {
      setBlocks((prevBlocks) => {
        return [
          ...prevBlocks,
          {
            id: "error" + msgPackage.package_id,
            type: BlockType.Error,
            content: msgPackage.data,
            status: BlockStatus.Error,
            start: 0,
            end: msgPackage.data.length - 1,
          },
        ];
      });
    }

    // TODO structured

    if (msgPackage.status === MessagePackageStatus.Finished) {
      buffer.current = "";
    }
  };

  useEffect(() => {
    const time = Date.now();
    if (timer.current) {
      clearTimeout(timer.current);
    }
    if (time - lastRenderTime.current < 100) {
      timer.current = window.setTimeout(() => {
        updateBlocks();
      }, 100);
      return;
    }

    lastRenderTime.current = time;
    updateBlocks();

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [msgPackage.data]);

  return blocks.map((i) => {
    if (i.type === BlockType.Thinking) {
      return <ThinkingBlock key={i.id} content={i.content} status={i.status} />;
    }
    if (i.type === BlockType.Text) {
      return <TextBlock key={i.id} content={i.content} />;
    }
    if (i.type === BlockType.Embedded) {
      return <EmbeddedBlock key={i.id} content={i.content} />;
    }
    if (i.type === BlockType.Error) {
      return <ErrorBlock key={i.id} content={i.content} status={i.status} />;
    }
  });
};

export default PackageRender;
