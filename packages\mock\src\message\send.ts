import type { IncomingMessage, ServerResponse } from "node:http";

import { MessagePackageType } from "../types.js";
import { ServerTransmitter } from "./transmitter.js";

export function* createMessage(message: string, step = 50) {
  const transmitter = new ServerTransmitter("conversationId", "messageId", "agentCode");
  yield transmitter.start();

  let index = 0;
  while (index < message.length) {
    const chunkMessage = message.slice(index, index + step);
    yield transmitter.sendMessage(chunkMessage, {
      packageType: MessagePackageType.Text,
      isLast: index >= message.length,
      isNewPackage: index === 0,
    });
    index += step;
  }
  yield transmitter.end();
}

export class MessageMocker {
  constructor(
    private req: IncomingMessage,
    private res: ServerResponse,
    private length: number,
  ) {
    req.socket.setKeepAlive(true);
    req.socket.setNoDelay(true);
    req.socket.setTimeout(0);
    res.writeHead(200, {
      "Content-Type": "text/event-stream; charset=utf-8",
      "Transfer-Encoding": "identity",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });
  }

  start(message: string) {
    const messageGenerator = createMessage(message, this.length);

    const timer = setInterval(() => {
      const result = messageGenerator.next();
      if (result.done) {
        clearInterval(timer);
        this.res.end();
        return;
      }
      this.res.write(result.value);
    }, 200);
  }
}
