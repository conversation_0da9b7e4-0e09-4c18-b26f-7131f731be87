import React from "react";

export function createEditableTag(placeholder: string, defaultValue: string = "&#xFEFF;") {
  return `<embedded-editable-tag placeholder="${placeholder}">${defaultValue}</embedded-editable-tag>`;
}

export function createSelect(
  placeholder: string,
  options: Array<{ label: string; value: string }>,
  defaultValue?: string,
  tooltips?: string,
  mode?: string
) {
  const optionsStr = JSON.stringify(options).replace(/"/g, "&quot;");
  return `<embedded-select placeholder="${placeholder}" options="${optionsStr}" defaultValue="${defaultValue || ""}" tooltips="${tooltips || ""}" mode="${mode || ""}"></embedded-select>`;
}

export function createApiSelect(params: {
  apiConfig: {
    url: string;
    method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    params?: Record<string, unknown>;
    data?: Record<string, unknown>;
    mapper?: {
      label: string;
      value: string;
    };
  };
  placeholder?: string;
  defaultValue?: string;
  tooltips?: string;
  style?: React.CSSProperties;
}) {
  const { apiConfig, placeholder, defaultValue, tooltips, style } = params;
  const apiConfigStr = JSON.stringify(apiConfig).replace(/"/g, "&quot;");
  const styleStr = JSON.stringify(style).replace(/"/g, "&quot;");
  return `<embedded-select placeholder="${placeholder}" apiConfig="${apiConfigStr}" defaultValue="${defaultValue || ""}" tooltips="${tooltips || ""}" style="${styleStr}"></embedded-select>`;
}
