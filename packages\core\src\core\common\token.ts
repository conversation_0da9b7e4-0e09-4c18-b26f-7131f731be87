// 从localStorage获取Token
export function getToken() {
  const tokenStr = localStorage.getItem("_token");

  if (tokenStr) {
    try {
      // 尝试解析JSON字符串
      return JSON.parse(tokenStr).access_token;
    } catch (e) {
      console.error("Failed to parse token:", e);
    }
  }
  return null;
}

export function setToken(token: string) {
  localStorage.setItem("_token", JSON.stringify({ access_token: token }));
}
