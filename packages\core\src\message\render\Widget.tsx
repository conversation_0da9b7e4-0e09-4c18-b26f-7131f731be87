import { Alert } from "antd";
import { nanoid } from "nanoid";
import React, { useContext, useMemo } from "react";

import { useActiveAgentConfig } from "@/core";
import { WidgetConfig } from "@/types";
import { buildInWidgets } from "@/widget";

import MessageContext from "../ui/Context";

interface WidgetRenderProps {
  config: WidgetConfig;
}

const WidgetRender: React.FC<WidgetRenderProps> = (props) => {
  const { config } = props;
  const agentConfig = useActiveAgentConfig();
  const messageContext = useContext(MessageContext);

  const widgets = useMemo(() => {
    const $widgets = agentConfig?.message?.blocks?.widgets ?? [];
    return [...buildInWidgets, ...$widgets];
  }, [agentConfig]);

  const widget = useMemo(() => {
    const widget = widgets.find((j) => j.code === config.code);
    if (!widget) {
      // 返回未知Widget占位符提示
      return {
        key: nanoid(),
        code: config.code,
        props: {} as any,
        component: () => <Alert message={`Render error: ${config.code} not found`} type="error" showIcon />,
      };
    }

    return {
      key: nanoid(),
      ...widget,
      props: {
        ...widget.props,
        ...config.props,
      },
    };
  }, [config, widgets]);

  const widgetProps = useMemo(() => {
    const parsedWidgetProps = { ...widget.props };
    if (widget.props) {
      const keys = Object.keys(widget.props);
      keys.forEach((k) => {
        const propValue = widget.props?.[k];
        let value: any = propValue;
        // 判断字符串类型的值是否按照表达式解析
        if (typeof propValue === "string") {
          const valueText: string = propValue.trim() ?? "";
          if (valueText.startsWith("{{") && valueText.endsWith("}}")) {
            // 解析表达式，state.variableName
            const expression = valueText.replace(/^{{|}}$/g, "");
            value = new Function("state", `return ${expression};`)(messageContext?.messageState ?? {});
          } else {
            // 尝试解析为 json 数据
            try {
              value = JSON.parse(valueText);
            } catch {}
          }
        }

        parsedWidgetProps[k] = value;
      });
    }
    return parsedWidgetProps;
  }, [widget, messageContext?.messageState]);

  return <widget.component key={widget.key} {...widgetProps} />;
};

export default WidgetRender;
