/**
 * Generate command implementation
 */

import { existsSync, statSync } from "fs";
import { resolve } from "path";

import inquirer from "inquirer";

import type { GenerateCommandOptions, WidgetGenerationOptions } from "../types.js";
import { Logger } from "../utils/logger.js";
import { ProjectAnalyzer } from "../utils/project-analyzer.js";
import { WidgetGenerator } from "../utils/widget-generator.js";

export class GenerateCommand {
  private projectAnalyzer: ProjectAnalyzer;
  private widgetGenerator: WidgetGenerator;

  constructor() {
    this.projectAnalyzer = new ProjectAnalyzer();
    this.widgetGenerator = new WidgetGenerator();
  }

  /**
   * Validate widget name
   */
  private validateWidgetName(name: string): { valid: boolean; error?: string } {
    if (!name) {
      return { valid: false, error: "Widget name is required" };
    }

    if (!/^[A-Z][a-zA-Z0-9]*$/.test(name)) {
      return { valid: false, error: "Widget name must be <PERSON><PERSON><PERSON> and start with uppercase letter" };
    }

    if (name.length < 3) {
      return { valid: false, error: "Widget name must be at least 3 characters long" };
    }

    return { valid: true };
  }

  /**
   * Validate target path
   */
  private validateTargetPath(targetPath: string): { valid: boolean; error?: string } {
    const resolvedPath = resolve(targetPath);

    if (!existsSync(resolvedPath)) {
      return { valid: false, error: `Target path does not exist: ${resolvedPath}` };
    }

    if (!statSync(resolvedPath).isDirectory()) {
      return { valid: false, error: `Target path is not a directory: ${resolvedPath}` };
    }

    return { valid: true };
  }

  /**
   * Validate placement and slot combination
   */
  private validatePlacementSlotCombination(
    placement: "message" | "sender" | "sidePanel",
    slot?: "blocks" | "header" | "footer" | "headerPanel" | "render",
  ): boolean {
    const validCombinations: Record<string, string[]> = {
      message: ["blocks", "header", "footer"],
      sender: ["footer", "headerPanel", "header"],
      sidePanel: ["render"],
    };

    if (!slot) {
      return false;
    }

    return validCombinations[placement]?.includes(slot) || false;
  }

  /**
   * Interactive mode for widget generation
   */
  private async runInteractive(): Promise<void> {
    Logger.title("Widget Generator");
    Logger.info("Let's create a new widget component for your CSCS Agent project");

    // First, get basic widget information
    const basicAnswers = await inquirer.prompt([
      {
        type: "input",
        name: "name",
        message: "Widget name (PascalCase):",
        validate: (input: string) => {
          const validation = this.validateWidgetName(input);
          return validation.valid || validation.error || "Invalid widget name";
        },
      },
      {
        type: "input",
        name: "targetPath",
        message: "Target project path:",
        default: process.cwd(),
        validate: (input: string) => {
          const validation = this.validateTargetPath(input);
          return validation.valid || validation.error || "Invalid target path";
        },
      },
      {
        type: "input",
        name: "description",
        message: "Widget description (optional):",
      },
    ]);

    // Analyze available configuration sections
    const availableSections = this.projectAnalyzer.getAvailableConfigurationSections();

    if (availableSections.length === 0) {
      Logger.error("No configuration sections found. Please ensure you have a valid CSCS Agent project.");
      process.exit(1);
    }

    // Present configuration section selection
    Logger.newLine();
    Logger.info("Select where you want to place this widget in your agent configuration:");

    const sectionAnswer = await inquirer.prompt([
      {
        type: "list",
        name: "selectedSection",
        message: "Configuration section:",
        choices: availableSections.map((section) => ({
          name: `${section.name} - ${section.description}`,
          value: section,
        })),
        pageSize: 10,
      },
    ]);

    const selectedSection = sectionAnswer.selectedSection;

    // Generate widget with selected configuration
    const options: WidgetGenerationOptions = {
      name: basicAnswers.name,
      type: "basic",
      targetPath: basicAnswers.targetPath,
      placement: selectedSection.placement,
      slot: selectedSection.slot,
      description: basicAnswers.description,
    };

    Logger.newLine();
    Logger.info(`Generating widget for: ${selectedSection.name}`);
    Logger.info(`Configuration path: ${selectedSection.path}`);

    const success = await this.widgetGenerator.generateWidget(options);

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Non-interactive mode
   */
  private async runNonInteractive(options: GenerateCommandOptions): Promise<void> {
    // Validate required options
    if (!options.name) {
      Logger.error("Widget name is required. Use --name option or run in interactive mode.");
      process.exit(1);
    }

    // Validate widget name
    const nameValidation = this.validateWidgetName(options.name);
    if (!nameValidation.valid) {
      Logger.error(nameValidation.error || "Invalid widget name");
      process.exit(1);
    }

    // Validate target path
    const targetPath = options.targetPath || process.cwd();
    const pathValidation = this.validateTargetPath(targetPath);
    if (!pathValidation.valid) {
      Logger.error(pathValidation.error || "Invalid target path");
      process.exit(1);
    }

    // Validate placement and slot combination
    const placement = options.placement || "message";
    const slot = options.slot || (placement === "message" ? "blocks" : placement === "sidePanel" ? "render" : "footer");

    if (!this.validatePlacementSlotCombination(placement, slot)) {
      Logger.error(`Invalid placement and slot combination: ${placement}.${slot}`);
      Logger.info("Valid combinations:");
      Logger.list([
        "message.blocks - Components within message content",
        "message.header - Components at message top",
        "message.footer - Components at message bottom",
        "sender.footer - Components in input area footer",
        "sender.headerPanel - Components in expandable header panel",
        "sidePanel.render - Components in side panel",
      ]);
      process.exit(1);
    }

    // Generate widget with specified or default settings
    const generationOptions: WidgetGenerationOptions = {
      name: options.name,
      type: "basic",
      targetPath,
      placement,
      slot,
      description: options.description,
      props: options.props ? JSON.parse(options.props) : undefined,
    };

    Logger.info(`Generating widget for: ${placement}.${slot}`);

    const success = await this.widgetGenerator.generateWidget(generationOptions);

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Execute generate command
   */
  async execute(options: GenerateCommandOptions = {}): Promise<void> {
    try {
      if (options.interactive || !options.name) {
        await this.runInteractive();
      } else {
        await this.runNonInteractive(options);
      }
    } catch (error) {
      Logger.error(`Failed to generate widget: ${error instanceof Error ? error.message : "Unknown error"}`);
      process.exit(1);
    }
  }
}
