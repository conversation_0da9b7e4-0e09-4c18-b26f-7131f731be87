import { Transforms } from "slate";

/**
 * RangePicker 组件使用示例和工具函数
 */

// 基本日期范围选择器
export const insertBasicRangePicker = (editor: any) => {
  const rangePickerText = `<embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="," tooltips="选择日期范围" disabled="false"></embedded-rangepicker>`;
  editor.insertText(rangePickerText);
};

// 带时间的日期范围选择器
export const insertDateTimeRangePicker = (editor: any) => {
  const rangePickerText = `<embedded-rangepicker placeholder="开始时间,结束时间" defaultValue="," tooltips="选择日期时间范围" disabled="false" format="YYYY-MM-DD HH:mm:ss" showTime="true"></embedded-rangepicker>`;
  editor.insertText(rangePickerText);
};

// 自定义分隔符的日期范围选择器
export const insertCustomSeparatorRangePicker = (editor: any) => {
  const rangePickerText = `<embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="," tooltips="选择日期范围" disabled="false" separator=" 至 "></embedded-rangepicker>`;
  editor.insertText(rangePickerText);
};

// 月份范围选择器
export const insertMonthRangePicker = (editor: any) => {
  const rangePickerText = `<embedded-rangepicker placeholder="开始月份,结束月份" defaultValue="," tooltips="选择月份范围" disabled="false" format="YYYY-MM"></embedded-rangepicker>`;
  editor.insertText(rangePickerText);
};

// 使用 Slate API 插入基本日期范围选择器
export const insertRangePickerWithSlateAPI = (editor: any) => {
  Transforms.insertNodes(editor, {
    type: "embedded-rangepicker",
    placeholder: ["开始日期", "结束日期"] as any,
    defaultValue: ["", ""] as any,
    tooltips: "选择日期范围",
    disabled: false,
    format: "YYYY-MM-DD",
    showTime: false,
    allowClear: true,
    separator: "~",
    children: [{ text: "" }],
  });
};

// 使用 Slate API 插入日期时间范围选择器
export const insertDateTimeRangePickerWithSlateAPI = (editor: any) => {
  Transforms.insertNodes(editor, {
    type: "embedded-rangepicker",
    placeholder: ["开始时间", "结束时间"] as any,
    defaultValue: ["2024-01-01 00:00:00", "2024-12-31 23:59:59"] as any,
    tooltips: "选择日期时间范围",
    disabled: false,
    format: "YYYY-MM-DD HH:mm:ss",
    showTime: true,
    allowClear: true,
    separator: " ~ ",
    children: [{ text: "" }],
  });
};

// 预设日期范围的选择器
export const insertPresetRangePicker = (editor: any) => {
  const today = new Date();
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

  const rangePickerText = `<embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="${today.toISOString().split("T")[0]},${nextWeek.toISOString().split("T")[0]}" tooltips="选择日期范围" disabled="false"></embedded-rangepicker>`;
  editor.insertText(rangePickerText);
};

/**
 * 工具函数：获取 RangePicker 的值
 */
export const getRangePickerValue = (element: any): [string, string] | null => {
  if (element.type === "embedded-rangepicker" && element.defaultValue) {
    return Array.isArray(element.defaultValue) ? element.defaultValue : ["", ""];
  }
  return null;
};

/**
 * 工具函数：设置 RangePicker 的值
 */
export const setRangePickerValue = (element: any, startDate: string, endDate: string) => {
  if (element.type === "embedded-rangepicker") {
    element.defaultValue = [startDate, endDate];
  }
};

/**
 * 工具函数：清空 RangePicker 的值
 */
export const clearRangePickerValue = (element: any) => {
  if (element.type === "embedded-rangepicker") {
    element.defaultValue = ["", ""];
  }
};
