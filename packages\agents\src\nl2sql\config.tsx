import { AgentConfig, Role } from "@cscs-agent/core";
import { Copy, Rating } from "@cscs-agent/presets";

import DataSourceSelect from "./widgets/data-source-select/DataSourceSelect";

export const nl2sqlAgentConfig: AgentConfig = {
  name: "SQL生成器",
  code: "nl2sql-agent",
  logo: "/assets/nl2sql-logo.png",
  welcome: "Hi，欢迎使用SQL生成器",
  description: "根据自然语言生成SQL",
  message: {
    blocks: {
      widgets: [],
    },
    slots: {
      footer: {
        widgets: [
          {
            code: "Copy",
            component: Copy,
            role: Role.AI,
          },
          {
            code: "Rating",
            component: Rating,
            role: Role.AI,
          },
          {
            code: "Copy",
            component: Copy,
            role: Role.HUMAN,
          },
        ],
      },
    },
  },
  prompts: [],
  commands: [
    {
      name: "",
      action: () => {},
    },
  ],
  suggestions: [],
  sender: {
    slots: {
      headerPanel: {
        title: "模板",
        widgets: [],
      },
      footer: {
        widgets: [
          {
            code: "DataSourceSelect",
            component: DataSourceSelect,
            props: {
              dataSourceApiUrl: "/gateway/nl2sql/v1/connections/",
            },
          },
        ],
      },
    },
    headerPanel: {
      enable: false,
    },
  },
  sidePanel: {
    render: {
      widgets: [],
    },
  },
  request: {
    chat: {
      url: "/chat/completion",
      headers: {},
      method: "get",
    },
  },
};
