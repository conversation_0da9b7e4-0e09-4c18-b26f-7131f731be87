@import "tailwindcss" prefix(ag);
@import "./theme/basic.css";
@import "./theme/highlight.css";
@import "./theme/markdown.css";
@import "katex/dist/katex.min.css";

/** antdX 覆盖*/
.ant-conversations-label {
  color: var(--agent-text-black-85) !important;
}

.ant-conversations .ant-conversations-group-title {
  padding-left: 12px !important;
}

.ant-conversations .ant-conversations-group-title .ant-typography {
  font-size: 12px !important;
}

.ant-conversations-group-title .ant-typography {
  color: var(--agent-color-black-65) !important;
}

.ant-conversations-item:hover {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active .ant-conversations-label {
  color: var(--agent-color-black-85) !important;
}

.ant-sender-actions-btn-disabled {
  background-color: rgba(37, 45, 62, 0.25) !important;
  opacity: 1 !important;
}

.ant-sender {
  background-color: #ffffff;
}

/* ProseMirror */
.ProseMirror {
  word-break: break-all;
}

.ProseMirror-focused {
  outline: none;
}

/* bubble */
.cscs-agent-bubble .cscs-agent-bubble-footer-human {
  visibility: hidden;
}

.cscs-agent-bubble:hover .cscs-agent-bubble-footer-human {
  visibility: visible;
}

/** Sender */
.cscs-agent-sender-editor::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.cscs-agent-sender-editor::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.cscs-agent-sender-editor::-webkit-scrollbar-track {
  background-color: transparent;
}

.cscs-agent-sender-header-panel {
  box-shadow: 0px 9px 28px 0px rgba(0,0,0,0.08), 0px 6px 16px -0px rgba(0,0,0,0.08)
}

/** conversation */
.conversation-scrollable::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.conversation-scrollable::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.conversation-scrollable::-webkit-scrollbar-track {
  background-color: transparent;
}

