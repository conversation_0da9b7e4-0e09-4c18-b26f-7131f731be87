import { ConfigProvider } from "antd";
import zhC<PERSON> from "antd/locale/zh_CN";
import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router";

import {
  DefaultErrorHandlerOption,
  DefaultRequestInterceptorOptions,
  setDefaultErrorHandlerOptions,
  setDefaultRequestInterceptorOptions,
} from "@/request";
import { createDefaultRouter } from "@/router";
import { antdThemeTokens } from "@/theme/antd-theme";
import { AgentChatConfig } from "@/types";
import { setBaseUrl } from "@/utils";

import { setToken } from "../common/token";
import { setLoginUrl } from "../common/vars";
import AgentChatHost from "../host/AgentChatHost";

interface initAppOptions {
  defaultRequestInterceptorOptions?: Partial<DefaultRequestInterceptorOptions>;
  defaultErrorHandlerOption?: Partial<DefaultErrorHandlerOption>;
  baseUrl?: string;
  loginUrl?: string;
  router?: ReturnType<typeof createDefaultRouter>;
  customCreateAppRoot?: () => void;
  agentChatConfig: AgentChatConfig;
}

export async function initApp(
  options: initAppOptions,
  lifecycle?: {
    onBeforeInit?: () => Promise<void> | void;
    onAfterInit?: () => Promise<void> | void;
    onError?: (error: Error) => Promise<void> | void;
  },
) {
  const {
    router,
    defaultRequestInterceptorOptions,
    defaultErrorHandlerOption,
    baseUrl,
    loginUrl,
    customCreateAppRoot,
    agentChatConfig,
  } = options;
  const { onBeforeInit, onAfterInit, onError } = lifecycle || {};

  if (!agentChatConfig) {
    console.error("Missing agentChatConfig!");
    return;
  }

  try {
    if (onBeforeInit) {
      await onBeforeInit();
    }

    loadTokenFromUrl();
    // 设置默认请求拦截器选项
    if (defaultRequestInterceptorOptions) {
      setDefaultRequestInterceptorOptions(defaultRequestInterceptorOptions);
    }
    // 设置默认错误处理选项
    if (defaultErrorHandlerOption) {
      setDefaultErrorHandlerOptions(defaultErrorHandlerOption);
    }
    // 设置基础 URL 和登录 URL
    if (baseUrl) {
      setBaseUrl(baseUrl);
    }
    // 设置登录 URL
    if (loginUrl) {
      setLoginUrl(loginUrl);
    }
    // 初始化路由
    if (!customCreateAppRoot) {
      if (router && agentChatConfig) {
        createAppRoot(router, agentChatConfig);
      } else {
        console.error("Router is not defined");
      }
    } else {
      customCreateAppRoot();
    }

    if (onAfterInit) {
      await onAfterInit();
    }
  } catch (error) {
    if (onError) {
      await onError(error as Error);
    } else {
      console.error("App initialization error:", error);
    }
  }
}

function createAppRoot(router: ReturnType<typeof createDefaultRouter>, agentChatConfig: AgentChatConfig) {
  createRoot(document.getElementById("root")!).render(
    <ConfigProvider
      locale={zhCN}
      theme={{
        cssVar: true,
        hashed: false,
        token: {
          ...antdThemeTokens,
        },
      }}
    >
      <AgentChatHost config={agentChatConfig} router={router}>
        <RouterProvider router={router}></RouterProvider>
      </AgentChatHost>
    </ConfigProvider>,
  );
}

function loadTokenFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  if (token) {
    setToken(token);
  }
}
